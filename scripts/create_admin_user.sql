-- 创建管理后台用户脚本
-- 用户名: admin
-- 密码: Qtt$123456

-- 检查用户是否已存在
SELECT 'Checking if admin user already exists...' AS status;

-- 如果用户已存在，显示警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'WARNING: Admin user already exists!'
        ELSE 'Admin user does not exist, proceeding with creation...'
    END AS check_result
FROM admin_users 
WHERE account = 'admin';

-- 插入管理后台用户
-- 注意：这里直接存储明文密码，在生产环境中应该使用哈希密码
INSERT INTO admin_users (account, password, created_at, updated_at) 
VALUES ('admin', 'Qtt$123456', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    updated_at = NOW();

-- 验证用户创建结果
SELECT 'Admin user creation completed!' AS status;

-- 显示创建的用户信息
SELECT 
    id,
    account,
    '***' AS password_hidden,
    created_at,
    updated_at
FROM admin_users 
WHERE account = 'admin';

-- 显示使用说明
SELECT '
=== 管理后台用户创建完成 ===

用户信息:
- 用户名: admin
- 密码: Qtt$123456

登录接口:
POST /admin/users/login

请求参数:
{
  "account": "admin",
  "password": "Qtt$123456"
}

注意事项:
1. 请妥善保管用户名和密码
2. 建议在生产环境中使用更强的密码
3. 密码当前以明文存储，建议后续改为哈希存储
' AS instructions;
