# Actix Web

## 启动项目

### 本地开发

```bash
SERVER_PORT=8081 cargo run
```
### 生产环境打包
```
APP_ENV=production cargo build --release --target x86_64-unknown-linux-musl
```


### 预发环境打包

```bash
APP_ENV=pre cargo build --release --target x86_64-unknown-linux-musl
```
## CICD
在rust-ci.yml中，github自带的


## TODO
1. 生产环境数据建表
2. 配置要区分环境
3. Nginx配置
4. 域名解析

5. 要写个脚本刷封面，每天把那天封面存在，但不是我的oss域名的收藏夹更新成oss域名

写代码的时候，遵循下面的逻辑：
  . 参考项目已有的项目接口和编码规范还有风格
  . 设计代码逻辑的时候，单个文件不要超过200行，如果超过了，就封装到单独的文件中
  . 设计代码逻辑的时候，必要的时候进行适当的封装函数，确保代码逻辑清晰易读
  . 中文回答，写完后尝试编译
  . 除了我当前提到的业务和相关逻辑，不要修改任何其他不相关的代码
  . 写代码的过程中，不需要向我解释，直接写代码就可以。最后给我总结就行了
  . 如果需要新建表，确保新建的表的字段都是可空的
  . 如果在此次实现逻辑的过程中修改了某个接口，那么需要在对应的handle入口处，注释写清楚请求参数和返回数据的格式
  . 最后，写完代码尝试编译测试环境，确保编译通过
  . 涉及管理后台的前端代码写完之后不需要重启前端

我的需求是：
管理后台的登录界面太丑了，要精致，像商业级别的管理后台登录界面