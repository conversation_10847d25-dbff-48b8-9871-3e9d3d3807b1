<template>
  <div class="dashboard-container">
    <el-container>
      <!-- 头部 -->
      <el-header class="header">
        <div class="header-left">
          <h1>管理后台</h1>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-icon><User /></el-icon>
              {{ currentUser?.account || '管理员' }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="200px" class="sidebar">
          <el-menu
            default-active="dashboard"
            class="sidebar-menu"
            :collapse="false"
          >
            <el-menu-item index="dashboard">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="users">
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="content">
              <el-icon><Document /></el-icon>
              <span>内容管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <div class="welcome-card">
            <el-card>
              <h2>欢迎使用管理后台</h2>
              <p>当前登录用户: {{ currentUser?.account }}</p>
              <p>用户ID: {{ currentUser?.user_id }}</p>
            </el-card>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, ArrowDown, House, Document } from '@element-plus/icons-vue'
import { getCurrentAdmin, adminLogout } from '@/api/auth'

const router = useRouter()

// 当前用户信息
const currentUser = ref(null)

// 获取当前用户信息
const loadCurrentUser = () => {
  currentUser.value = getCurrentAdmin()
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      // 执行登出
      await adminLogout()
      ElMessage.success('已退出登录')
      
      // 跳转到登录页
      router.push('/login')
    } catch (error) {
      // 用户取消操作
      if (error !== 'cancel') {
        console.error('退出登录失败:', error)
      }
    }
  }
}

onMounted(() => {
  loadCurrentUser()
})
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
}

.header-left h1 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.sidebar {
  background: #304156;
}

.sidebar-menu {
  border: none;
  background: #304156;
}

:deep(.el-menu-item) {
  color: #bfcbd9;
}

:deep(.el-menu-item:hover) {
  background-color: #263445;
  color: #fff;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff;
  color: #fff;
}

.main-content {
  background: #f0f2f5;
  padding: 20px;
}

.welcome-card {
  max-width: 800px;
}

.welcome-card h2 {
  color: #333;
  margin-bottom: 16px;
}

.welcome-card p {
  color: #666;
  margin: 8px 0;
}
</style>
